'use client'

import { useGoogleTaxonomy } from '@/hooks/use-google-taxonomy';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Loader2, CheckCircle, AlertCircle, Globe } from 'lucide-react';

export function GoogleTaxonomyTest() {
  const { 
    nodes, 
    isLoading, 
    error, 
    getCategoryCount,
    getTopLevelCategories 
  } = useGoogleTaxonomy();

  const topCategories = getTopLevelCategories();
  const isUsingFallback = getCategoryCount() < 100;

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Globe className="h-5 w-5 text-[#ff0074]" />
          <span>Teste da Taxonomia Google</span>
          {isUsingFallback && (
            <Badge variant="outline" className="text-orange-600 border-orange-300">
              Fallback
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Status */}
        <div className="flex items-center space-x-2">
          {isLoading ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
              <span className="text-sm">Carregando taxonomia...</span>
            </>
          ) : error ? (
            <>
              <AlertCircle className="h-4 w-4 text-red-500" />
              <span className="text-sm text-red-600">Erro: {error}</span>
            </>
          ) : (
            <>
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span className="text-sm text-green-600">
                {getCategoryCount()} categorias carregadas
                {isUsingFallback ? ' (modo fallback)' : ' (taxonomia completa)'}
              </span>
            </>
          )}
        </div>

        {/* Estatísticas */}
        {!isLoading && !error && (
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div className="text-lg font-bold text-[#ff0074]">
                {getCategoryCount()}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400">
                Total de Categorias
              </div>
            </div>
            
            <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div className="text-lg font-bold text-[#5600ce]">
                {topCategories.length}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400">
                Categorias Principais
              </div>
            </div>
          </div>
        )}

        {/* Lista de Categorias Principais */}
        {!isLoading && !error && topCategories.length > 0 && (
          <div>
            <h4 className="text-sm font-medium mb-2">Categorias Principais:</h4>
            <div className="flex flex-wrap gap-2">
              {topCategories.slice(0, 8).map((category) => (
                <Badge 
                  key={category.id} 
                  variant="secondary"
                  className="text-xs"
                >
                  {category.name}
                </Badge>
              ))}
              {topCategories.length > 8 && (
                <Badge variant="outline" className="text-xs">
                  +{topCategories.length - 8} mais
                </Badge>
              )}
            </div>
          </div>
        )}

        {/* Debug Info */}
        {process.env.NODE_ENV === 'development' && (
          <details className="text-xs text-gray-500">
            <summary className="cursor-pointer">Debug Info</summary>
            <pre className="mt-2 p-2 bg-gray-100 dark:bg-gray-800 rounded text-xs overflow-auto">
              {JSON.stringify({
                nodesLength: nodes.length,
                isLoading,
                error,
                isUsingFallback,
                topCategoriesCount: topCategories.length,
                firstCategory: topCategories[0] || null
              }, null, 2)}
            </pre>
          </details>
        )}
      </CardContent>
    </Card>
  );
}
