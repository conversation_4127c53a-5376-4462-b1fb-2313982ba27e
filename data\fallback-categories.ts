/**
 * 🎯 CATEGORIAS DE MARKETING DE INFLUÊNCIA
 * Taxonomia específica para influenciadores e marketing digital
 * Baseada em nichos reais do mercado de influência
 */

export const fallbackCategories = [
  // Categorias específicas para influenciadores
  {
    id: "1",
    name: "Lifestyle e Vlogs",
    fullPath: "Lifestyle e Vlogs",
    level: 1,
    children: [
      {
        id: "101",
        name: "Vlogs Diários",
        fullPath: "Lifestyle e Vlogs > Vlogs Diários",
        level: 2,
        children: []
      },
      {
        id: "102",
        name: "Rotina e Produtividade",
        fullPath: "Lifestyle e Vlogs > Rotina e Produtividade",
        level: 2,
        children: []
      },
      {
        id: "103",
        name: "Decoração e Casa",
        fullPath: "Lifestyle e Vlogs > Decoração e Casa",
        level: 2,
        children: []
      },
      {
        id: "104",
        name: "Relacionamentos",
        fullPath: "Lifestyle e Vlogs > Relacionamentos",
        level: 2,
        children: []
      }
    ]
  },
  {
    id: "2",
    name: "<PERSON><PERSON> e Beleza",
    fullPath: "<PERSON>da e Beleza",
    level: 1,
    children: [
      {
        id: "201",
        name: "<PERSON><PERSON> Feminina",
        fullPath: "Moda e Beleza > Moda Feminina",
        level: 2,
        children: []
      },
      {
        id: "202",
        name: "Moda Masculina",
        fullPath: "Moda e Beleza > Moda Masculina",
        level: 2,
        children: []
      },
      {
        id: "203",
        name: "Maquiagem e Skincare",
        fullPath: "Moda e Beleza > Maquiagem e Skincare",
        level: 2,
        children: []
      },
      {
        id: "204",
        name: "Cabelo e Unhas",
        fullPath: "Moda e Beleza > Cabelo e Unhas",
        level: 2,
        children: []
      }
    ]
  },
  {
    id: "2",
    name: "Tecnologia",
    fullPath: "Tecnologia",
    level: 1,
    children: [
      {
        id: "201",
        name: "Smartphones",
        fullPath: "Tecnologia > Smartphones",
        level: 2,
        children: []
      },
      {
        id: "202",
        name: "Computadores",
        fullPath: "Tecnologia > Computadores",
        level: 2,
        children: []
      },
      {
        id: "203",
        name: "Gaming",
        fullPath: "Tecnologia > Gaming",
        level: 2,
        children: []
      }
    ]
  },
  {
    id: "3",
    name: "Saúde e Fitness",
    fullPath: "Saúde e Fitness",
    level: 1,
    children: [
      {
        id: "301",
        name: "Suplementos",
        fullPath: "Saúde e Fitness > Suplementos",
        level: 2,
        children: []
      },
      {
        id: "302",
        name: "Equipamentos de Exercício",
        fullPath: "Saúde e Fitness > Equipamentos de Exercício",
        level: 2,
        children: []
      },
      {
        id: "303",
        name: "Nutrição",
        fullPath: "Saúde e Fitness > Nutrição",
        level: 2,
        children: []
      }
    ]
  },
  {
    id: "4",
    name: "Casa e Jardim",
    fullPath: "Casa e Jardim",
    level: 1,
    children: [
      {
        id: "401",
        name: "Decoração",
        fullPath: "Casa e Jardim > Decoração",
        level: 2,
        children: []
      },
      {
        id: "402",
        name: "Móveis",
        fullPath: "Casa e Jardim > Móveis",
        level: 2,
        children: []
      },
      {
        id: "403",
        name: "Jardinagem",
        fullPath: "Casa e Jardim > Jardinagem",
        level: 2,
        children: []
      }
    ]
  },
  {
    id: "5",
    name: "Alimentação e Bebidas",
    fullPath: "Alimentação e Bebidas",
    level: 1,
    children: [
      {
        id: "501",
        name: "Comida Saudável",
        fullPath: "Alimentação e Bebidas > Comida Saudável",
        level: 2,
        children: []
      },
      {
        id: "502",
        name: "Bebidas",
        fullPath: "Alimentação e Bebidas > Bebidas",
        level: 2,
        children: []
      },
      {
        id: "503",
        name: "Receitas",
        fullPath: "Alimentação e Bebidas > Receitas",
        level: 2,
        children: []
      }
    ]
  },
  {
    id: "6",
    name: "Viagem e Turismo",
    fullPath: "Viagem e Turismo",
    level: 1,
    children: [
      {
        id: "601",
        name: "Destinos",
        fullPath: "Viagem e Turismo > Destinos",
        level: 2,
        children: []
      },
      {
        id: "602",
        name: "Hospedagem",
        fullPath: "Viagem e Turismo > Hospedagem",
        level: 2,
        children: []
      },
      {
        id: "603",
        name: "Atividades",
        fullPath: "Viagem e Turismo > Atividades",
        level: 2,
        children: []
      }
    ]
  },
  {
    id: "7",
    name: "Automóveis",
    fullPath: "Automóveis",
    level: 1,
    children: [
      {
        id: "701",
        name: "Carros",
        fullPath: "Automóveis > Carros",
        level: 2,
        children: []
      },
      {
        id: "702",
        name: "Motocicletas",
        fullPath: "Automóveis > Motocicletas",
        level: 2,
        children: []
      },
      {
        id: "703",
        name: "Acessórios Automotivos",
        fullPath: "Automóveis > Acessórios Automotivos",
        level: 2,
        children: []
      }
    ]
  },
  {
    id: "8",
    name: "Entretenimento",
    fullPath: "Entretenimento",
    level: 1,
    children: [
      {
        id: "801",
        name: "Filmes e TV",
        fullPath: "Entretenimento > Filmes e TV",
        level: 2,
        children: []
      },
      {
        id: "802",
        name: "Música",
        fullPath: "Entretenimento > Música",
        level: 2,
        children: []
      },
      {
        id: "803",
        name: "Livros",
        fullPath: "Entretenimento > Livros",
        level: 2,
        children: []
      }
    ]
  },
  {
    id: "9",
    name: "Esportes",
    fullPath: "Esportes",
    level: 1,
    children: [
      {
        id: "901",
        name: "Futebol",
        fullPath: "Esportes > Futebol",
        level: 2,
        children: []
      },
      {
        id: "902",
        name: "Basquete",
        fullPath: "Esportes > Basquete",
        level: 2,
        children: []
      },
      {
        id: "903",
        name: "Tênis",
        fullPath: "Esportes > Tênis",
        level: 2,
        children: []
      }
    ]
  },
  {
    id: "10",
    name: "Educação",
    fullPath: "Educação",
    level: 1,
    children: [
      {
        id: "1001",
        name: "Cursos Online",
        fullPath: "Educação > Cursos Online",
        level: 2,
        children: []
      },
      {
        id: "1002",
        name: "Idiomas",
        fullPath: "Educação > Idiomas",
        level: 2,
        children: []
      },
      {
        id: "1003",
        name: "Desenvolvimento Pessoal",
        fullPath: "Educação > Desenvolvimento Pessoal",
        level: 2,
        children: []
      }
    ]
  }
];

export const fallbackCategoriesCount = fallbackCategories.reduce((total, category) => {
  return total + 1 + category.children.length;
}, 0);
