import { Metadata } from 'next';
import { HybridCategoryManager } from '@/components/hybrid-category-manager';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Globe, Info, Zap } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

export const metadata: Metadata = {
  title: 'Categorias Google | Admin - DeuMatch',
  description: 'Gerenciar e importar categorias da taxonomia oficial do Google Product Categories',
};

export default function GoogleCategoriesPage() {
  return (
    <div className="container mx-auto py-8 px-4 max-w-6xl bg-white min-h-screen">
      {/* Cabeçalho */}
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-4">
          <div className="p-2 bg-[#ff0074]/10 rounded-lg">
            <Globe className="h-6 w-6 text-[#ff0074]" />
          </div>
          <div>
            <h1 className="text-3xl font-bold tracking-tight text-black">Categorias do Google</h1>
            <p className="text-gray-600">
              Importe e gerencie categorias da taxonomia oficial do Google Product Categories
            </p>
          </div>
        </div>
      </div>

      {/* Informações sobre a Taxonomia do Google */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <Card className="border-blue-200 bg-blue-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-blue-800">
              <Info className="h-5 w-5" />
              Sobre a Taxonomia Google
            </CardTitle>
          </CardHeader>
          <CardContent className="text-blue-700">
            <ul className="space-y-2 text-sm">
              <li className="flex items-start gap-2">
                <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                <span><strong>+6.000 categorias</strong> organizadas hierarquicamente</span>
              </li>
              <li className="flex items-start gap-2">
                <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                <span><strong>Padrão internacional</strong> usado pelo Google Shopping</span>
              </li>
              <li className="flex items-start gap-2">
                <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                <span><strong>Múltiplos idiomas</strong> incluindo português brasileiro</span>
              </li>
              <li className="flex items-start gap-2">
                <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                <span><strong>Atualização automática</strong> direto dos servidores Google</span>
              </li>
            </ul>
          </CardContent>
        </Card>

        <Card className="border-green-200 bg-green-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-green-800">
              <Zap className="h-5 w-5" />
              Vantagens da Integração
            </CardTitle>
          </CardHeader>
          <CardContent className="text-green-700">
            <ul className="space-y-2 text-sm">
              <li className="flex items-start gap-2">
                <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                <span><strong>Economia de tempo:</strong> Sem necessidade de criar categorias manualmente</span>
              </li>
              <li className="flex items-start gap-2">
                <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                <span><strong>Padronização:</strong> Categorias reconhecidas globalmente</span>
              </li>
              <li className="flex items-start gap-2">
                <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                <span><strong>SEO otimizado:</strong> Melhor indexação em mecanismos de busca</span>
              </li>
              <li className="flex items-start gap-2">
                <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                <span><strong>Compatibilidade:</strong> Integração com plataformas de e-commerce</span>
              </li>
            </ul>
          </CardContent>
        </Card>
      </div>

      {/* Alerta Informativo */}
      <Alert className="mb-8 border-orange-200 bg-orange-50">
        <Info className="h-4 w-4 text-orange-600" />
        <AlertDescription className="text-orange-700">
          <strong>Como funciona:</strong> As categorias importadas do Google serão adicionadas ao seu sistema 
          e poderão ser usadas normalmente para classificar influenciadores. Você pode importar categorias 
          específicas ou usar a importação rápida para categorias principais.
        </AlertDescription>
      </Alert>

      {/* Gerenciador Principal */}
      <HybridCategoryManager 
        onCategoriesImported={(count) => {
          console.log(`✅ ${count} categorias do Google importadas com sucesso!`);
        }}
      />

      {/* Rodapé com Informações Técnicas */}
      <div className="mt-12 pt-8 border-t border-gray-200">
        <div className="text-center text-sm text-gray-500">
          <p className="mb-2">
            <strong>Fonte:</strong> Google Product Taxonomy - 
            <a 
              href="https://support.google.com/merchants/answer/6324436" 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-[#ff0074] hover:underline ml-1"
            >
              Documentação Oficial
            </a>
          </p>
          <p>
            As categorias são carregadas diretamente dos servidores do Google e 
            mantidas atualizadas automaticamente.
          </p>
        </div>
      </div>
    </div>
  );
}
