'use client'

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Globe, 
  User, 
  Download, 
  Merge, 
  Loader2, 
  CheckCircle,
  AlertCircle 
} from 'lucide-react';
import { GoogleCategorySelector } from './google-category-selector';
import { useGoogleTaxonomy } from '@/hooks/use-google-taxonomy';
import { CategoryService } from '@/services/category-service-unified';
import { useFirebaseAuth } from '@/hooks/use-clerk-auth';
import { toast } from 'sonner';

interface HybridCategoryManagerProps {
  onCategoriesImported?: (count: number) => void;
}

export function HybridCategoryManager({ 
  onCategoriesImported 
}: HybridCategoryManagerProps) {
  const [selectedGoogleCategories, setSelectedGoogleCategories] = useState<string[]>([]);
  const [isImporting, setIsImporting] = useState(false);
  const [importedCount, setImportedCount] = useState(0);
  
  const { user } = useFirebaseAuth();
  const { 
    nodes, 
    categories, 
    isLoading, 
    error, 
    getCategoryCount,
    findNodeById 
  } = useGoogleTaxonomy();

  /**
   * 📥 IMPORTAR CATEGORIAS SELECIONADAS
   * Adiciona as categorias do Google ao sistema do usuário
   */
  const handleImportSelected = async () => {
    if (!user?.uid || selectedGoogleCategories.length === 0) {
      toast.error('Selecione pelo menos uma categoria para importar');
      return;
    }

    setIsImporting(true);
    let successCount = 0;

    try {
      for (const categoryId of selectedGoogleCategories) {
        const node = findNodeById(categoryId);
        if (!node) continue;

        try {
          await CategoryService.createCategory(user.uid, {
            name: node.name,
            description: `Importada do Google: ${node.fullPath}`,
            color: generateColorFromId(node.id),
            isActive: true
          });
          
          successCount++;
        } catch (error) {
          console.warn(`Erro ao importar categoria ${node.name}:`, error);
          // Continuar com as próximas categorias
        }
      }

      setImportedCount(successCount);
      setSelectedGoogleCategories([]);
      
      toast.success(`${successCount} categorias importadas com sucesso!`);
      onCategoriesImported?.(successCount);
      
    } catch (error) {
      console.error('Erro durante importação:', error);
      toast.error('Erro ao importar categorias');
    } finally {
      setIsImporting(false);
    }
  };

  /**
   * 📥 IMPORTAR CATEGORIAS DE NÍVEL SUPERIOR
   * Importa todas as categorias principais do Google
   */
  const handleImportTopLevel = async () => {
    if (!user?.uid) {
      toast.error('Usuário não autenticado');
      return;
    }

    setIsImporting(true);
    let successCount = 0;

    try {
      const topLevelNodes = nodes.filter(node => node.level === 1);
      
      for (const node of topLevelNodes) {
        try {
          await CategoryService.createCategory(user.uid, {
            name: node.name,
            description: `Categoria principal do Google: ${node.fullPath}`,
            color: generateColorFromId(node.id),
            isActive: true
          });
          
          successCount++;
        } catch (error) {
          console.warn(`Erro ao importar categoria ${node.name}:`, error);
        }
      }

      setImportedCount(successCount);
      
      toast.success(`${successCount} categorias principais importadas!`);
      onCategoriesImported?.(successCount);
      
    } catch (error) {
      console.error('Erro durante importação:', error);
      toast.error('Erro ao importar categorias principais');
    } finally {
      setIsImporting(false);
    }
  };

  /**
   * 🎨 GERAR COR BASEADA NO ID
   */
  const generateColorFromId = (id: string): string => {
    const colors = [
      '#ff0074', '#5600ce', '#270038', '#9810fa', 
      '#ff6b35', '#f7931e', '#ffd23f', '#06ffa5',
      '#1fb3d3', '#5d4e75', '#b83dba', '#ff4081'
    ];
    
    const hash = id.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
    return colors[hash % colors.length];
  };

  if (error) {
    return (
      <Card className="border-red-200 bg-red-50 dark:bg-red-900/20">
        <CardContent className="pt-6">
          <div className="flex items-center space-x-2 text-red-600 dark:text-red-400">
            <AlertCircle className="h-5 w-5" />
            <span>Erro ao carregar taxonomia do Google: {error}</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header com Estatísticas */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Globe className="h-5 w-5 text-[#ff0074]" />
            <span>Gerenciador Híbrido de Categorias</span>
          </CardTitle>
          <CardDescription>
            Combine suas categorias personalizadas com a taxonomia oficial do Google
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div className="text-2xl font-bold text-[#ff0074]">
                {isLoading ? (
                  <Loader2 className="h-6 w-6 animate-spin mx-auto" />
                ) : (
                  getCategoryCount().toLocaleString()
                )}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Categorias Google
              </div>
            </div>
            
            <div className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div className="text-2xl font-bold text-[#5600ce]">
                {selectedGoogleCategories.length}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Selecionadas
              </div>
            </div>
            
            <div className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {importedCount}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Importadas
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tabs Principal */}
      <Tabs defaultValue="selector" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="selector" className="flex items-center space-x-2">
            <Globe className="h-4 w-4" />
            <span>Seletor Google</span>
          </TabsTrigger>
          <TabsTrigger value="import" className="flex items-center space-x-2">
            <Download className="h-4 w-4" />
            <span>Importação</span>
          </TabsTrigger>
        </TabsList>

        {/* Tab: Seletor de Categorias */}
        <TabsContent value="selector" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Selecionar Categorias do Google</CardTitle>
              <CardDescription>
                Escolha as categorias que deseja importar para seu sistema
              </CardDescription>
            </CardHeader>
            <CardContent>
              <GoogleCategorySelector
                selectedCategories={selectedGoogleCategories}
                onCategoriesChange={setSelectedGoogleCategories}
                maxSelections={50}
                placeholder="Buscar e selecionar categorias do Google..."
              />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Tab: Importação */}
        <TabsContent value="import" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Importação Selecionada */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center space-x-2">
                  <Merge className="h-5 w-5" />
                  <span>Importar Selecionadas</span>
                </CardTitle>
                <CardDescription>
                  Importar apenas as categorias que você selecionou
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  {selectedGoogleCategories.length} categorias selecionadas
                </div>
                
                <Button
                  onClick={handleImportSelected}
                  disabled={selectedGoogleCategories.length === 0 || isImporting || !user}
                  className="w-full bg-[#ff0074] hover:bg-[#ff0074]/90"
                >
                  {isImporting ? (
                    <div className="flex items-center space-x-2">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      <span>Importando...</span>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-2">
                      <Download className="h-4 w-4" />
                      <span>Importar Selecionadas</span>
                    </div>
                  )}
                </Button>
              </CardContent>
            </Card>

            {/* Importação Rápida */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center space-x-2">
                  <Globe className="h-5 w-5" />
                  <span>Importação Rápida</span>
                </CardTitle>
                <CardDescription>
                  Importar todas as categorias principais do Google
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  {nodes.filter(n => n.level === 1).length} categorias principais
                </div>
                
                <Button
                  onClick={handleImportTopLevel}
                  disabled={isImporting || !user}
                  variant="outline"
                  className="w-full"
                >
                  {isImporting ? (
                    <div className="flex items-center space-x-2">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      <span>Importando...</span>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-2">
                      <Download className="h-4 w-4" />
                      <span>Importar Principais</span>
                    </div>
                  )}
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Status da Importação */}
          {importedCount > 0 && (
            <Card className="border-green-200 bg-green-50 dark:bg-green-900/20">
              <CardContent className="pt-6">
                <div className="flex items-center space-x-2 text-green-600 dark:text-green-400">
                  <CheckCircle className="h-5 w-5" />
                  <span>
                    {importedCount} categorias importadas com sucesso! 
                    Agora você pode usá-las em seus influenciadores.
                  </span>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
