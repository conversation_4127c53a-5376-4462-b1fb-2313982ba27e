import { NextRequest, NextResponse } from 'next/server';

/**
 * 🌍 API ROUTE PARA TAXONOMIA DO GOOGLE
 * Proxy para carregar a taxonomia oficial do Google Product Categories
 * Resolve problemas de CORS e permite cache no servidor
 */

interface GoogleTaxonomyParams {
  language?: string;
  includeIds?: string;
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const language = searchParams.get('language') || 'pt-BR';
    const includeIds = searchParams.get('includeIds') === 'true';

    console.log(`🌍 [API] Carregando taxonomia Google: ${language}, includeIds: ${includeIds}`);

    // Construir URL da taxonomia do Google
    const filename = includeIds 
      ? `taxonomy-with-ids.${language}.txt`
      : `taxonomy.${language}.txt`;
    
    const googleUrl = `https://www.google.com/basepages/producttype/${filename}`;

    // Fazer requisição para o Google
    const response = await fetch(googleUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/plain, */*',
        'Accept-Language': 'pt-BR,pt;q=0.9,en;q=0.8',
      },
      // Cache por 1 hora
      next: { revalidate: 3600 }
    });

    if (!response.ok) {
      console.error(`❌ [API] Erro ao carregar taxonomia: ${response.status} ${response.statusText}`);
      return NextResponse.json(
        { 
          error: 'Erro ao carregar taxonomia do Google',
          status: response.status,
          statusText: response.statusText
        }, 
        { status: response.status }
      );
    }

    const text = await response.text();
    
    if (!text || text.trim().length === 0) {
      console.error('❌ [API] Taxonomia vazia recebida do Google');
      return NextResponse.json(
        { error: 'Taxonomia vazia recebida do Google' }, 
        { status: 500 }
      );
    }

    console.log(`✅ [API] Taxonomia carregada com sucesso: ${text.split('\n').length} linhas`);

    // Retornar com headers apropriados
    return new NextResponse(text, {
      status: 200,
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Cache-Control': 'public, max-age=3600', // Cache por 1 hora
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    });

  } catch (error) {
    console.error('❌ [API] Erro interno ao carregar taxonomia:', error);
    
    return NextResponse.json(
      { 
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      }, 
      { status: 500 }
    );
  }
}

// Permitir CORS para requisições OPTIONS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
