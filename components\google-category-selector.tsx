'use client'

import { useState, useEffect } from 'react';
import { Search, ChevronRight, Loader2, Globe, Tag } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '@/components/ui/dialog';
import { 
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import { useGoogleTaxonomy, useGoogleTaxonomySearch } from '@/hooks/use-google-taxonomy';
import { GoogleTaxonomyNode } from '@/services/google-taxonomy-service';

interface GoogleCategorySelectorProps {
  selectedCategories?: string[];
  onCategoriesChange?: (categories: string[]) => void;
  maxSelections?: number;
  placeholder?: string;
  className?: string;
  mode?: 'single' | 'multiple';
}

export function GoogleCategorySelector({
  selectedCategories = [],
  onCategoriesChange,
  maxSelections = 5,
  placeholder = "Selecionar categorias do Google...",
  className = "",
  mode = 'multiple'
}: GoogleCategorySelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedNodes, setSelectedNodes] = useState<GoogleTaxonomyNode[]>([]);
  
  const { 
    nodes, 
    isLoading, 
    error, 
    findNodeById, 
    getTopLevelCategories,
    getCategoryCount 
  } = useGoogleTaxonomy();
  
  const { 
    searchTerm, 
    setSearchTerm, 
    results 
  } = useGoogleTaxonomySearch();

  // Sincronizar categorias selecionadas
  useEffect(() => {
    const foundNodes = selectedCategories
      .map(id => findNodeById(id))
      .filter(Boolean) as GoogleTaxonomyNode[];
    
    setSelectedNodes(foundNodes);
  }, [selectedCategories, findNodeById]);

  /**
   * 🎯 SELECIONAR CATEGORIA
   */
  const handleSelectCategory = (node: GoogleTaxonomyNode) => {
    let newSelected: string[];

    if (mode === 'single') {
      newSelected = [node.id];
      setIsOpen(false);
    } else {
      const isAlreadySelected = selectedCategories.includes(node.id);
      
      if (isAlreadySelected) {
        newSelected = selectedCategories.filter(id => id !== node.id);
      } else {
        if (selectedCategories.length >= maxSelections) {
          return; // Limite atingido
        }
        newSelected = [...selectedCategories, node.id];
      }
    }

    onCategoriesChange?.(newSelected);
  };

  /**
   * 🗑️ REMOVER CATEGORIA
   */
  const handleRemoveCategory = (nodeId: string) => {
    const newSelected = selectedCategories.filter(id => id !== nodeId);
    onCategoriesChange?.(newSelected);
  };

  /**
   * 🎨 RENDERIZAR ITEM DE CATEGORIA
   */
  const renderCategoryItem = (node: GoogleTaxonomyNode) => {
    const isSelected = selectedCategories.includes(node.id);
    const pathParts = node.fullPath.split(' > ');
    
    return (
      <CommandItem
        key={node.id}
        value={node.id}
        onSelect={() => handleSelectCategory(node)}
        className={`flex items-center justify-between p-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800 ${
          isSelected ? 'bg-[#ff0074]/10 border-l-2 border-[#ff0074]' : ''
        }`}
      >
        <div className="flex-1">
          <div className="font-medium text-sm">{node.name}</div>
          <div className="text-xs text-gray-500 mt-1">
            {pathParts.slice(0, -1).join(' > ')}
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <Badge variant="outline" className="text-xs">
            ID: {node.id}
          </Badge>
          {isSelected && (
            <Badge className="bg-[#ff0074] text-white text-xs">
              Selecionado
            </Badge>
          )}
        </div>
      </CommandItem>
    );
  };

  if (error) {
    return (
      <div className="p-4 border border-red-200 rounded-lg bg-red-50 dark:bg-red-900/20">
        <div className="flex items-center space-x-2 text-red-600 dark:text-red-400">
          <Globe className="h-4 w-4" />
          <span className="text-sm">Erro ao carregar categorias: {error}</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Categorias Selecionadas */}
      {selectedNodes.length > 0 && (
        <div className="space-y-2">
          <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Categorias Selecionadas ({selectedNodes.length}/{mode === 'single' ? 1 : maxSelections})
          </div>
          <div className="flex flex-wrap gap-2">
            {selectedNodes.map((node) => (
              <Badge
                key={node.id}
                variant="secondary"
                className="flex items-center space-x-1 bg-[#ff0074]/10 text-[#ff0074] border-[#ff0074]/20"
              >
                <Tag className="h-3 w-3" />
                <span className="text-xs">{node.name}</span>
                <button
                  onClick={() => handleRemoveCategory(node.id)}
                  className="ml-1 hover:bg-[#ff0074]/20 rounded-full p-0.5"
                >
                  ×
                </button>
              </Badge>
            ))}
          </div>
        </div>
      )}

      {/* Seletor Principal */}
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogTrigger asChild>
          <Button
            variant="outline"
            className="w-full justify-start text-left font-normal"
            disabled={isLoading}
          >
            {isLoading ? (
              <div className="flex items-center space-x-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>Carregando categorias...</span>
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <Globe className="h-4 w-4" />
                <span>{placeholder}</span>
                {nodes.length > 0 && (
                  <Badge variant="outline" className="ml-auto">
                    {getCategoryCount().toLocaleString()} categorias
                  </Badge>
                )}
              </div>
            )}
          </Button>
        </DialogTrigger>

        <DialogContent className="max-w-2xl max-h-[80vh]">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <Globe className="h-5 w-5 text-[#ff0074]" />
              <span>Categorias do Google ({getCategoryCount().toLocaleString()})</span>
            </DialogTitle>
          </DialogHeader>

          <Command className="rounded-lg border shadow-md">
            <CommandInput
              placeholder="Buscar categorias..."
              value={searchTerm}
              onValueChange={setSearchTerm}
            />
            
            <CommandList>
              <ScrollArea className="h-96">
                {searchTerm ? (
                  // Resultados da busca
                  <CommandGroup heading={`Resultados (${results.length})`}>
                    {results.length === 0 ? (
                      <CommandEmpty>Nenhuma categoria encontrada.</CommandEmpty>
                    ) : (
                      results.map(renderCategoryItem)
                    )}
                  </CommandGroup>
                ) : (
                  // Categorias de nível superior
                  <CommandGroup heading="Categorias Principais">
                    {getTopLevelCategories().map(renderCategoryItem)}
                  </CommandGroup>
                )}
              </ScrollArea>
            </CommandList>
          </Command>

          <div className="flex justify-between items-center text-xs text-gray-500 pt-2 border-t">
            <span>
              {mode === 'multiple' 
                ? `${selectedCategories.length}/${maxSelections} selecionadas`
                : selectedCategories.length > 0 ? '1 selecionada' : 'Nenhuma selecionada'
              }
            </span>
            <span>Taxonomia oficial do Google</span>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

/**
 * 🎯 VERSÃO COMPACTA PARA FORMULÁRIOS
 */
export function CompactGoogleCategorySelector({
  selectedCategory,
  onCategoryChange,
  placeholder = "Selecionar categoria..."
}: {
  selectedCategory?: string;
  onCategoryChange?: (category: string | null) => void;
  placeholder?: string;
}) {
  return (
    <GoogleCategorySelector
      selectedCategories={selectedCategory ? [selectedCategory] : []}
      onCategoriesChange={(categories) => {
        onCategoryChange?.(categories[0] || null);
      }}
      mode="single"
      maxSelections={1}
      placeholder={placeholder}
    />
  );
}
