'use client'

import { useState, useEffect, useCallback } from 'react';
import {
  GoogleTaxonomyService,
  GoogleTaxonomyNode,
  GoogleTaxonomyOptions,
  Category
} from '@/services/google-taxonomy-service';

interface UseGoogleTaxonomyReturn {
  // Estados
  nodes: GoogleTaxonomyNode[];
  categories: Category[];
  isLoading: boolean;
  error: string | null;
  
  // Ações
  loadTaxonomy: (options?: GoogleTaxonomyOptions) => Promise<void>;
  searchCategories: (term: string, maxResults?: number) => GoogleTaxonomyNode[];
  convertToCategories: (userId?: string | null) => Category[];
  
  // Utilitários
  findNodeById: (id: string) => GoogleTaxonomyNode | null;
  findNodeByName: (name: string) => GoogleTaxonomyNode | null;
  getNodePath: (nodeId: string) => string[];
  getTopLevelCategories: () => GoogleTaxonomyNode[];
  getCategoryCount: () => number;
}

export function useGoogleTaxonomy(): UseGoogleTaxonomyReturn {
  const [nodes, setNodes] = useState<GoogleTaxonomyNode[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * 🔄 CARREGAR TAXONOMIA
   */
  const loadTaxonomy = useCallback(async (
    options: GoogleTaxonomyOptions = {
      language: 'pt-BR',
      includeIds: true
    }
  ) => {
    setIsLoading(true);
    setError(null);

    try {
      console.log('🌍 [HOOK] Iniciando carregamento da taxonomia Google...');

      const taxonomyNodes = await GoogleTaxonomyService.loadGoogleTaxonomy(options);
      setNodes(taxonomyNodes);

      // Converter automaticamente para formato interno
      const internalCategories = GoogleTaxonomyService.convertToInternalCategories(
        taxonomyNodes,
        null // Categorias públicas por padrão
      );
      setCategories(internalCategories);

      console.log(`✅ [HOOK] ${taxonomyNodes.length} categorias carregadas com sucesso`);

    } catch (err) {
      console.error('❌ [HOOK] Erro ao carregar taxonomia, usando fallback:', err);

      // Não definir como erro, apenas usar fallback silenciosamente
      try {
        const fallbackNodes = await GoogleTaxonomyService.loadFallbackTaxonomy();
        setNodes(fallbackNodes);

        const internalCategories = GoogleTaxonomyService.convertToInternalCategories(
          fallbackNodes,
          null
        );
        setCategories(internalCategories);

        console.log(`✅ [HOOK] ${fallbackNodes.length} categorias de fallback carregadas`);
      } catch (fallbackErr) {
        const errorMessage = fallbackErr instanceof Error ? fallbackErr.message : 'Erro desconhecido';
        setError(errorMessage);
        console.error('❌ [HOOK] Erro crítico - fallback também falhou:', fallbackErr);
      }
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * 🔍 BUSCAR CATEGORIAS
   */
  const searchCategories = useCallback((
    term: string, 
    maxResults: number = 50
  ): GoogleTaxonomyNode[] => {
    if (!term.trim() || nodes.length === 0) return [];
    
    return GoogleTaxonomyService.searchCategories(nodes, term, maxResults);
  }, [nodes]);

  /**
   * 🔄 CONVERTER PARA CATEGORIAS INTERNAS
   */
  const convertToCategories = useCallback((
    userId: string | null = null
  ): Category[] => {
    if (nodes.length === 0) return [];
    
    const converted = GoogleTaxonomyService.convertToInternalCategories(nodes, userId);
    setCategories(converted);
    return converted;
  }, [nodes]);

  /**
   * 🔍 ENCONTRAR NÓ POR ID
   */
  const findNodeById = useCallback((id: string): GoogleTaxonomyNode | null => {
    const searchInNodes = (nodeList: GoogleTaxonomyNode[]): GoogleTaxonomyNode | null => {
      for (const node of nodeList) {
        if (node.id === id) return node;
        
        const found = searchInNodes(node.children);
        if (found) return found;
      }
      return null;
    };

    return searchInNodes(nodes);
  }, [nodes]);

  /**
   * 🔍 ENCONTRAR NÓ POR NOME
   */
  const findNodeByName = useCallback((name: string): GoogleTaxonomyNode | null => {
    const searchInNodes = (nodeList: GoogleTaxonomyNode[]): GoogleTaxonomyNode | null => {
      for (const node of nodeList) {
        if (node.name.toLowerCase() === name.toLowerCase()) return node;
        
        const found = searchInNodes(node.children);
        if (found) return found;
      }
      return null;
    };

    return searchInNodes(nodes);
  }, [nodes]);

  /**
   * 🛤️ OBTER CAMINHO DO NÓ
   */
  const getNodePath = useCallback((nodeId: string): string[] => {
    const node = findNodeById(nodeId);
    if (!node) return [];
    
    return node.fullPath.split(' > ').map(part => part.trim());
  }, [findNodeById]);

  /**
   * 📊 OBTER CATEGORIAS DE NÍVEL SUPERIOR
   */
  const getTopLevelCategories = useCallback((): GoogleTaxonomyNode[] => {
    return nodes.filter(node => node.level === 1);
  }, [nodes]);

  /**
   * 📈 OBTER CONTAGEM TOTAL
   */
  const getCategoryCount = useCallback((): number => {
    const countNodes = (nodeList: GoogleTaxonomyNode[]): number => {
      let count = nodeList.length;
      for (const node of nodeList) {
        count += countNodes(node.children);
      }
      return count;
    };

    return countNodes(nodes);
  }, [nodes]);

  // Carregar taxonomia automaticamente na primeira renderização
  useEffect(() => {
    if (nodes.length === 0 && !isLoading && !error) {
      // Tentar carregar fallback primeiro para garantir que funcione
      console.log('🚀 [HOOK] Carregando taxonomia de fallback primeiro...');
      GoogleTaxonomyService.loadFallbackTaxonomy()
        .then(fallbackNodes => {
          setNodes(fallbackNodes);
          const internalCategories = GoogleTaxonomyService.convertToInternalCategories(
            fallbackNodes,
            null
          );
          setCategories(internalCategories);
          console.log(`✅ [HOOK] Fallback carregado: ${fallbackNodes.length} categorias`);

          // Depois tentar carregar a taxonomia completa em background
          loadTaxonomy().catch(err => {
            console.log('ℹ️ [HOOK] Taxonomia completa não disponível, mantendo fallback');
          });
        })
        .catch(err => {
          console.error('❌ [HOOK] Erro crítico ao carregar fallback:', err);
          setError('Erro ao carregar categorias');
        });
    }
  }, [loadTaxonomy, nodes.length, isLoading, error]);

  return {
    // Estados
    nodes,
    categories,
    isLoading,
    error,
    
    // Ações
    loadTaxonomy,
    searchCategories,
    convertToCategories,
    
    // Utilitários
    findNodeById,
    findNodeByName,
    getNodePath,
    getTopLevelCategories,
    getCategoryCount
  };
}

/**
 * 🎯 HOOK SIMPLIFICADO PARA BUSCA
 * Para casos onde só precisa buscar categorias
 */
export function useGoogleTaxonomySearch() {
  const { searchCategories, isLoading, error } = useGoogleTaxonomy();
  const [searchTerm, setSearchTerm] = useState('');
  const [results, setResults] = useState<GoogleTaxonomyNode[]>([]);

  useEffect(() => {
    if (searchTerm.trim()) {
      const searchResults = searchCategories(searchTerm, 20);
      setResults(searchResults);
    } else {
      setResults([]);
    }
  }, [searchTerm, searchCategories]);

  return {
    searchTerm,
    setSearchTerm,
    results,
    isLoading,
    error
  };
}

/**
 * 🏷️ HOOK PARA CATEGORIAS DE NÍVEL SUPERIOR
 * Para navegação principal
 */
export function useTopLevelCategories() {
  const { getTopLevelCategories, isLoading, error } = useGoogleTaxonomy();
  const [topCategories, setTopCategories] = useState<GoogleTaxonomyNode[]>([]);

  useEffect(() => {
    const categories = getTopLevelCategories();
    setTopCategories(categories);
  }, [getTopLevelCategories]);

  return {
    topCategories,
    isLoading,
    error
  };
}
