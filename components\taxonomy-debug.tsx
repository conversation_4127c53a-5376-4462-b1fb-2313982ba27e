'use client'

import { useState } from 'react';
import { useGoogleTaxonomy } from '@/hooks/use-google-taxonomy';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Search, Bug } from 'lucide-react';

export function TaxonomyDebug() {
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState<any[]>([]);
  
  const { 
    nodes, 
    isLoading, 
    error, 
    searchCategories,
    getCategoryCount,
    getTopLevelCategories 
  } = useGoogleTaxonomy();

  const handleSearch = () => {
    console.log('🔍 Iniciando busca por:', searchTerm);
    console.log('📊 Dados disponíveis:', {
      nodesLength: nodes.length,
      isLoading,
      error,
      totalCategories: getCategoryCount()
    });
    
    if (searchTerm.trim()) {
      const results = searchCategories(searchTerm, 10);
      console.log('📋 Resultados da busca:', results);
      setSearchResults(results);
    } else {
      setSearchResults([]);
    }
  };

  const topCategories = getTopLevelCategories();

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Bug className="h-5 w-5 text-[#ff0074]" />
          <span>Debug da Taxonomia</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Status Geral */}
        <div className="grid grid-cols-3 gap-4">
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-lg font-bold">{nodes.length}</div>
            <div className="text-xs text-gray-600">Nós Carregados</div>
          </div>
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-lg font-bold">{getCategoryCount()}</div>
            <div className="text-xs text-gray-600">Total Categorias</div>
          </div>
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-lg font-bold">{topCategories.length}</div>
            <div className="text-xs text-gray-600">Categorias Principais</div>
          </div>
        </div>

        {/* Estado */}
        <div className="flex items-center space-x-2">
          <Badge variant={isLoading ? "default" : error ? "destructive" : "secondary"}>
            {isLoading ? "Carregando..." : error ? "Erro" : "Carregado"}
          </Badge>
          {error && <span className="text-sm text-red-600">{error}</span>}
        </div>

        {/* Teste de Busca */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Teste de Busca</h3>
          <div className="flex space-x-2">
            <Input
              placeholder="Digite um termo para buscar..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
            />
            <Button onClick={handleSearch}>
              <Search className="h-4 w-4 mr-2" />
              Buscar
            </Button>
          </div>
          
          {searchResults.length > 0 && (
            <div>
              <h4 className="font-medium mb-2">Resultados ({searchResults.length}):</h4>
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {searchResults.map((result, index) => (
                  <div key={index} className="p-2 bg-gray-50 rounded text-sm">
                    <div className="font-medium">{result.name}</div>
                    <div className="text-gray-600 text-xs">{result.fullPath}</div>
                    <div className="text-gray-500 text-xs">ID: {result.id}</div>
                  </div>
                ))}
              </div>
            </div>
          )}
          
          {searchTerm && searchResults.length === 0 && (
            <div className="text-sm text-gray-500">
              Nenhum resultado encontrado para "{searchTerm}"
            </div>
          )}
        </div>

        {/* Categorias Principais */}
        {topCategories.length > 0 && (
          <div>
            <h3 className="text-lg font-semibold mb-2">Categorias Principais</h3>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              {topCategories.map((category) => (
                <div key={category.id} className="p-2 bg-gray-50 rounded text-sm">
                  <div className="font-medium">{category.name}</div>
                  <div className="text-gray-600 text-xs">
                    {category.children.length} subcategorias
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Testes Rápidos */}
        <div>
          <h3 className="text-lg font-semibold mb-2">Testes Rápidos</h3>
          <div className="flex flex-wrap gap-2">
            {['moda', 'beleza', 'tecnologia', 'saúde', 'casa'].map((term) => (
              <Button
                key={term}
                variant="outline"
                size="sm"
                onClick={() => {
                  setSearchTerm(term);
                  const results = searchCategories(term, 5);
                  setSearchResults(results);
                }}
              >
                {term}
              </Button>
            ))}
          </div>
        </div>

        {/* Debug Raw Data */}
        <details className="text-xs">
          <summary className="cursor-pointer font-medium">Dados Brutos (Debug)</summary>
          <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto max-h-40">
            {JSON.stringify({
              nodesLength: nodes.length,
              firstNode: nodes[0] || null,
              isLoading,
              error,
              searchTerm,
              searchResultsLength: searchResults.length
            }, null, 2)}
          </pre>
        </details>
      </CardContent>
    </Card>
  );
}
