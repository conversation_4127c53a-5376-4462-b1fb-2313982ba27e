/**
 * 🌍 SERVIÇO DE TAXONOMIA DO GOOGLE
 * Integração com Google Product Taxonomy - +6.000 categorias prontas
 * Suporte a múltiplos idiomas e cache local
 */

import { Category } from './category-service-unified';

export interface GoogleTaxonomyNode {
  id: string;
  name: string;
  fullPath: string;
  parentId?: string;
  level: number;
  children: GoogleTaxonomyNode[];
}

export interface GoogleTaxonomyOptions {
  language: 'pt-BR' | 'en-US' | 'es-ES' | 'fr-FR' | 'de-DE';
  includeIds: boolean;
  maxLevel?: number;
}

export class GoogleTaxonomyService {
  private static cache = new Map<string, GoogleTaxonomyNode[]>();
  private static readonly BASE_URL = 'https://www.google.com/basepages/producttype';

  /**
   * 🔄 CARREGAR TAXONOMIA DO GOOGLE
   * Baixa e processa a taxonomia oficial do Google
   */
  static async loadGoogleTaxonomy(
    options: GoogleTaxonomyOptions = { 
      language: 'pt-BR', 
      includeIds: true 
    }
  ): Promise<GoogleTaxonomyNode[]> {
    const cacheKey = `${options.language}-${options.includeIds}`;
    
    // Verificar cache primeiro
    if (this.cache.has(cacheKey)) {
      console.log('📦 [CACHE] Taxonomia carregada do cache');
      return this.cache.get(cacheKey)!;
    }

    try {
      console.log(`🌍 [GOOGLE] Carregando taxonomia: ${options.language}`);
      
      const filename = options.includeIds 
        ? `taxonomy-with-ids.${options.language}.txt`
        : `taxonomy.${options.language}.txt`;
      
      const url = `${this.BASE_URL}/${filename}`;
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error(`Erro ao carregar taxonomia: ${response.status}`);
      }

      const text = await response.text();
      const nodes = this.parseTaxonomyText(text, options);
      
      // Salvar no cache
      this.cache.set(cacheKey, nodes);
      
      console.log(`✅ [GOOGLE] ${nodes.length} categorias carregadas`);
      return nodes;
      
    } catch (error) {
      console.error('❌ [GOOGLE] Erro ao carregar taxonomia:', error);
      throw error;
    }
  }

  /**
   * 🔍 PROCESSAR TEXTO DA TAXONOMIA
   * Converte o formato TXT do Google em estrutura hierárquica
   */
  private static parseTaxonomyText(
    text: string, 
    options: GoogleTaxonomyOptions
  ): GoogleTaxonomyNode[] {
    const lines = text.split('\n').filter(line => line.trim());
    const nodes: GoogleTaxonomyNode[] = [];
    const nodeMap = new Map<string, GoogleTaxonomyNode>();

    for (const line of lines) {
      if (line.startsWith('#')) continue; // Pular comentários
      
      const node = this.parseLineToNode(line, options);
      if (!node) continue;

      // Aplicar filtro de nível se especificado
      if (options.maxLevel && node.level > options.maxLevel) {
        continue;
      }

      nodeMap.set(node.id, node);
      
      if (node.level === 1) {
        // Categoria raiz
        nodes.push(node);
      } else {
        // Encontrar pai e adicionar como filho
        const parentPath = this.getParentPath(node.fullPath);
        const parent = this.findNodeByPath(nodeMap, parentPath);
        
        if (parent) {
          node.parentId = parent.id;
          parent.children.push(node);
        }
      }
    }

    return nodes;
  }

  /**
   * 📝 PROCESSAR LINHA INDIVIDUAL
   * Formato: "123 - Categoria > Subcategoria > Item"
   */
  private static parseLineToNode(
    line: string, 
    options: GoogleTaxonomyOptions
  ): GoogleTaxonomyNode | null {
    try {
      let id = '';
      let fullPath = '';

      if (options.includeIds) {
        // Formato com ID: "123 - Categoria > Subcategoria"
        const match = line.match(/^(\d+)\s*-\s*(.+)$/);
        if (!match) return null;
        
        id = match[1];
        fullPath = match[2];
      } else {
        // Formato sem ID: "Categoria > Subcategoria"
        fullPath = line;
        id = this.generateIdFromPath(fullPath);
      }

      const pathParts = fullPath.split(' > ').map(part => part.trim());
      const name = pathParts[pathParts.length - 1];
      const level = pathParts.length;

      return {
        id,
        name,
        fullPath,
        level,
        children: []
      };
      
    } catch (error) {
      console.warn('⚠️ [PARSE] Erro ao processar linha:', line, error);
      return null;
    }
  }

  /**
   * 🔗 CONVERTER PARA FORMATO INTERNO
   * Converte GoogleTaxonomyNode para Category do sistema
   */
  static convertToInternalCategories(
    nodes: GoogleTaxonomyNode[],
    userId: string | null = null
  ): Category[] {
    const categories: Category[] = [];

    const convertNode = (node: GoogleTaxonomyNode, parentId?: string): void => {
      const category: Category = {
        id: node.id,
        name: node.name,
        slug: this.generateSlug(node.name),
        description: `Categoria do Google: ${node.fullPath}`,
        color: this.generateColorFromId(node.id),
        count: null,
        userId: userId,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      categories.push(category);

      // Processar filhos recursivamente
      for (const child of node.children) {
        convertNode(child, node.id);
      }
    };

    for (const rootNode of nodes) {
      convertNode(rootNode);
    }

    return categories;
  }

  /**
   * 🎨 GERAR COR BASEADA NO ID
   * Cria cores consistentes para categorias
   */
  private static generateColorFromId(id: string): string {
    const colors = [
      '#ff0074', '#5600ce', '#270038', '#9810fa', 
      '#ff6b35', '#f7931e', '#ffd23f', '#06ffa5',
      '#1fb3d3', '#5d4e75', '#b83dba', '#ff4081'
    ];
    
    const hash = id.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
    return colors[hash % colors.length];
  }

  /**
   * 🔤 GERAR SLUG
   */
  private static generateSlug(name: string): string {
    return name
      .toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  }

  /**
   * 🔍 BUSCAR CATEGORIA POR TERMO
   */
  static searchCategories(
    nodes: GoogleTaxonomyNode[], 
    searchTerm: string,
    maxResults: number = 50
  ): GoogleTaxonomyNode[] {
    const results: GoogleTaxonomyNode[] = [];
    const term = searchTerm.toLowerCase();

    const searchRecursive = (node: GoogleTaxonomyNode): void => {
      if (results.length >= maxResults) return;

      if (node.name.toLowerCase().includes(term) || 
          node.fullPath.toLowerCase().includes(term)) {
        results.push(node);
      }

      for (const child of node.children) {
        searchRecursive(child);
      }
    };

    for (const rootNode of nodes) {
      searchRecursive(rootNode);
    }

    return results;
  }

  // Métodos auxiliares privados
  private static getParentPath(fullPath: string): string {
    const parts = fullPath.split(' > ');
    return parts.slice(0, -1).join(' > ');
  }

  private static findNodeByPath(
    nodeMap: Map<string, GoogleTaxonomyNode>, 
    path: string
  ): GoogleTaxonomyNode | undefined {
    for (const [_, node] of nodeMap) {
      if (node.fullPath === path) {
        return node;
      }
    }
    return undefined;
  }

  private static generateIdFromPath(path: string): string {
    // Gerar ID baseado no hash do caminho
    let hash = 0;
    for (let i = 0; i < path.length; i++) {
      const char = path.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Converter para 32bit integer
    }
    return Math.abs(hash).toString();
  }
}
